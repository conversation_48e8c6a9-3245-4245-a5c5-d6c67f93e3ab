<template>
    <!-- <teleport to="body"> -->
        <CarouselBanner 
            v-if="shouldShowBanner"
            :items="carouselItems"
            :buttonImg="buttonImg"
            @item-click="handleCarouselItemClick"
            @button-click="handleBannerButtonClick"
        />
        <div class="headbar-container" :class="{fixed: route.meta.fixedHeader,dark: route.meta.header_module == 'dark', 'digital-human-mode': isDigitalHumanPage}" :style="{ top: headbarTopPosition }">
            <el-scrollbar class="scrollbar-container" view-class="scrollbar-view flex flex_j_c-space-between flex_a_i-center">
                <!-- 数字人页面专用的左侧导航 -->
                <div v-if="isDigitalHumanPage" class="digital-human-nav flex flex_a_i-center">
                    <div class="back-btn flex flex_a_i-center cursor-pointer" 
                         :class="{ 'navigating': isNavigating }" 
                         @click="handleBack">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="back-icon">
                            <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <span class="digital-human-title cursor-pointer" 
                          :class="{ 'navigating': isNavigating }" 
                          @click="handleDigitalHumanTitleClick">数字人</span>
                </div>
                
                <!-- 数字人页面专用的中间标题编辑区域 -->
                <div v-if="isDigitalHumanPage" class="digital-human-title-edit flex flex_a_i-center">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="edit-icon">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <input 
                        v-model="projectTitle" 
                        class="title-input" 
                        placeholder="未命名作品"
                        @blur="handleTitleBlur"
                        @keydown.enter="handleTitleEnter"
                    />
                </div>
                
                <!-- 其他页面的导航 -->
                <div v-if="!isDigitalHumanPage" class="height-full flex flex_j_c-space-between flex_a_i-center">
                    <Logo />
                    <Sidebar />
                </div>
                
                <div class="right-container flex flex_a_i-center">
                    <div v-if="!isDigitalHumanPage && checkUserLogin() && shouldShowSaveButton" class="save-button-container">
                        <span class="save-button" @click="handleSave" :class="{ 'disabled': isLoading }">
                            保存
                        </span>
                    </div>
                    <Action />
                </div>
            </el-scrollbar>
        </div>
    <!-- </teleport> -->
</template>

<script setup>
import Logo from '../logo/index.vue'
// import Crumb from './components/crumb/index.vue'
import Action from '@/views/layout/components/headbar/components/action/index.vue'
import Sidebar from './components/sidebar/index.vue'
import CarouselBanner from '@/components/CarouselBanner.vue' // 导入新的轮播图组件
import { useRoute, useRouter } from 'vue-router';
import { reactive, defineProps, ref, computed, onMounted, onBeforeUnmount } from "vue";
import { ElMessage } from 'element-plus';
import { addVideoMaterial } from '@/api/material';
import { usePreviewStore } from '@/stores/previewStore';
import { useMusicStore } from '@/stores/modules/musicStore';
import { useloginStore } from '@/stores/login';
import { useUmeng } from '@/utils/umeng/hook'; // 导入友盟埋点
import { useDigitalHumanStore } from '@/views/modules/digitalHuman/store/digitalHumanStore'; // 导入数字人store

// 引入banner图片
import banner1 from '@/assets/img/a1.png';
import banner2 from '@/assets/img/a2.png';
import rightArrow from '@/assets/img/a3.png'; // 使用右箭头图标作为按钮

let route = useRoute();
let router = useRouter();

// 轮播图数据
const carouselItems = ref([
  { 
    imageUrl: banner1,
    link: 'https://mp.weixin.qq.com/s/FjC0pETSRSGPOC-I8CtGZA'
  },
  { 
    imageUrl: banner2,
    link: 'https://mp.weixin.qq.com/s/ozr2NeM-ShoXYpStIdtEwA?'
  }
]);

// 按钮图片
const buttonImg = ref(rightArrow);

// 当前窗口状态
const windowState = ref({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    pixelRatio: typeof window !== 'undefined' ? window.devicePixelRatio : 1
});

// 返回按钮loading状态
const isNavigating = ref(false);

// 保存窗口调整大小的事件监听器引用
let resizeListener = null;

// 计算属性：判断是否应该显示banner
const shouldShowBanner = computed(() => {
    const path = route.path;
    // 使用更精确的匹配方式，仅在H4Home路径下显示banner
    const allowedPaths = [ 
        '/H4Home'
    ];
    
    // 检查当前路径是否以允许的路径开头
    return allowedPaths.some(allowedPath => 
        path === allowedPath || 
        path.startsWith(`${allowedPath}/`)
    );
});

// 计算headbar的top值，基于是否显示banner
const headbarTopPosition = computed(() => {
    if (!shouldShowBanner.value) {
        return '0';
    }
    
    // 根据窗口宽度返回适当的高度值
    if (windowState.value.width <= 768) {
        return '36px'; // 移动设备
    } else if (windowState.value.width <= 1280) {
        return '40px'; // 小型笔记本
    } else if (windowState.value.width <= 1440) {
        return '45px'; // 标准笔记本
    } else if (windowState.value.width <= 1680) {
        return '50px'; // 大型笔记本/小型桌面
    } else {
        return '60px'; // 大型桌面显示器
    }
});

// 处理banner按钮点击
const handleBannerButtonClick = (data) => {
    // 获取当前活动的轮播图项目
    const currentItem = data.item;
    
    // 如果有链接，则打开链接
    if (currentItem && currentItem.link) {
        window.open(currentItem.link, '_blank');
    }
    // 切换到下一张已在组件内部处理
};

// 处理轮播项点击
const handleCarouselItemClick = (item) => {
    // 这里不需要重复打开链接，因为在CarouselBanner组件内部已经处理了链接打开
    // 如果需要在这里做其他处理可以添加
};

// 使用预览存储
const previewStore = usePreviewStore();

// 使用音乐存储
const musicStore = useMusicStore();

// 使用登录存储
const loginStore = useloginStore();

// 使用数字人存储
const digitalHumanStore = useDigitalHumanStore();

// 初始化埋点
const umeng = useUmeng();

// 加载状态
const isLoading = ref(false);

// 计算属性：判断当前页面是否应该显示保存按钮
const shouldShowSaveButton = computed(() => {
    const path = route.path;
    return path.includes('/ContentCreation') || 
           path.includes('/MusicAudio') || 
           path.includes('/VideoEditing') || 
           path.includes('/VoiceOver');
});

// 计算属性：判断是否为数字人页面
const isDigitalHumanPage = computed(() => {
    return route.path === '/digital-human-transition' ||
           route.name === 'DigitalHumanTransition' ||
           route.path === '/digital-human-editor-page' ||
           route.name === 'DigitalHumanEditorPage';
});

// 处理返回按钮点击
const handleBack = async () => {
    // 防止重复点击
    if (isNavigating.value) {
        return;
    }

    isNavigating.value = true;

    try {
        // 如果在数字人页面，先暂停播放
        if (isDigitalHumanPage.value && digitalHumanStore.isPlaying) {
            digitalHumanStore.pause();
            console.log('🎵 已暂停数字人播放');
        }

        // 添加埋点
        umeng.trackEvent('数字人编辑器', '点击返回按钮', '返回首页', '');

        // 直接跳转到首页，避免使用 router.back() 可能导致的浏览器历史问题
        await router.push({ name: 'home' });

        console.log('🔄 成功返回首页');
    } catch (error) {
        console.error('返回首页失败:', error);
        ElMessage.error('返回失败，请重试');
    } finally {
        // 延迟重置状态，防止快速连续点击
        setTimeout(() => {
            isNavigating.value = false;
        }, 500);
    }
};

// 处理数字人标题点击，跳转到首页
const handleDigitalHumanTitleClick = async () => {
    // 防止重复点击
    if (isNavigating.value) {
        return;
    }

    isNavigating.value = true;

    try {
        // 如果在数字人页面，先暂停播放
        if (isDigitalHumanPage.value && digitalHumanStore.isPlaying) {
            digitalHumanStore.pause();
            console.log('🎵 已暂停数字人播放');
        }

        // 添加埋点
        umeng.trackEvent('数字人编辑器', '点击标题', '返回首页', '');

        // 直接跳转到首页，确保行为一致性
        await router.push({ name: 'home' });

        console.log('🔄 成功返回首页');
    } catch (error) {
        console.error('返回首页失败:', error);
        ElMessage.error('返回失败，请重试');
    } finally {
        // 延迟重置状态，防止快速连续点击
        setTimeout(() => {
            isNavigating.value = false;
        }, 500);
    }
};

// 项目标题
const projectTitle = ref('');

// 处理标题失焦
const handleTitleBlur = () => {
    // 保存标题到本地存储或发送到服务器
    console.log('保存标题:', projectTitle.value);
};

// 处理回车键
const handleTitleEnter = (event) => {
    event.target.blur();
};

// 🔧 暴露获取标题的方法，供父组件调用
const getProjectTitle = () => {
    return projectTitle.value || '';
};

// 🔧 暴露设置标题的方法，供父组件调用
const setProjectTitle = (title) => {
    projectTitle.value = title || '';
    console.log('📝 Headbar组件设置标题:', title);
};

// 暴露方法给父组件
defineExpose({
    getProjectTitle,
    setProjectTitle
});

let props = defineProps({
    fixedHeader: {
        type: Boolean,
        default: false
    },
});

// 判断用户是否已登录
const checkUserLogin = () => {
    // 检查 Pinia store 中是否有 token
    if (loginStore.token) {
        return true;
    }
    
    // 如果 store 中没有，再从本地存储获取 user 信息
    const userStorage = localStorage.getItem('user');
    if (!userStorage) return false;
    
    try {
        const userData = JSON.parse(userStorage);
        // 检查 token 是否存在且不为空
        const isLoggedIn = userData && userData.token && userData.token.trim() !== '';
        
        // 如果本地存储有 token 但 store 没有，同步到 store
        if (isLoggedIn && !loginStore.token) {
            loginStore.setToken(userData.token);
            if (userData.userId) loginStore.setUserId(userData.userId);
            if (userData) loginStore.setLoginData(userData);
        }
        
        return isLoggedIn;
    } catch (e) {
        console.error('解析用户数据失败:', e);
        return false;
    }
};

// 自定义函数获取用户ID，当userId为null时使用默认值'11'
const getUserId = () => {
    return loginStore.userId || '11';
};

/**
 * 从错误对象中提取错误信息
 * @param {Error|string|object} error - 错误对象
 * @returns {string} - 友好的错误消息
 */
const getErrorMessage = (error) => {
    if (typeof error === 'string') {
        return error;
    }
    
    if (error.message) {
        return error.message;
    }
    
    if (error.response && error.response.data && error.response.data.message) {
        return error.response.data.message;
    }
    
    // 默认错误信息
    return '发生未知错误，请重试';
};

/**
 * 处理保存操作
 */
const handleSave = async () => {
    // 添加埋点代码 - 确保是函数第一行执行
    umeng.trackEvent(
      '编辑器', 
      '点击保存按钮', 
      `页面: ${route.name || '未知页面'}`, 
      ''
    );
    
    // 检查用户是否已登录
    if (!checkUserLogin()) {
        ElMessage.warning('请先登录');
        return;
    }

    // 已经在加载中，防止重复点击
    if (isLoading.value) {
        return;
    }

    // 设置加载状态
    isLoading.value = true;

    try {
        // 从预览存储中获取数据
        const title = previewStore.title || '';
        const content = previewStore.content || '';
        
        // 获取视频素材
        let ossPath = '';
        // 从previewStore.currentVideoUrl获取
        if (previewStore.currentVideoUrl) {
            ossPath = previewStore.currentVideoUrl;
        }
        // 从pinia的videoList获取
        else if (previewStore.videoList && previewStore.videoList.length > 0 && previewStore.videoList[0].url) {
            ossPath = previewStore.videoList[0].url;
        }
        
        // 获取音乐素材
        let musicMaterialUrl = '';
        // 从musicStore获取
        const musicStoreData = musicStore.musicList || [];
        if (musicStoreData.length > 0) {
            const musicItem = musicStoreData[0];
            if (musicItem.url) {
                musicMaterialUrl = musicItem.url;
            } else if (musicItem.src) {
                musicMaterialUrl = musicItem.src;
            } else if (musicItem.path) {
                musicMaterialUrl = musicItem.path;
            } else if (musicItem.musicUrl) {
                musicMaterialUrl = musicItem.musicUrl;
            } else if (typeof musicItem === 'string') {
                musicMaterialUrl = musicItem;
            }
        }
        
        // 获取配音角色URL
        let audioUrl = '';
        if (previewStore.selectedRole) {
            const storeRole = previewStore.selectedRole;
            if (storeRole.audioUrl) {
                audioUrl = storeRole.audioUrl;
            } else if (storeRole.url) {
                audioUrl = storeRole.url;
            }
        }
        
        // 构建API参数
        const params = {
            userId: getUserId(),
            copywrite: content,
            status: "1",
            isDeleted: "0"
        };

        // 添加其他可选参数
        if (title) {
            params.title = title;
            params.name = title;
        }
        if (ossPath) params.ossPath = ossPath;
        if (musicMaterialUrl) params.musicMaterialUrl = musicMaterialUrl;
        if (audioUrl) params.audioUrl = audioUrl;
        
        // 检查URL中是否有id参数
        if (route.query.id) {
            // 优先使用URL中的id参数
            params.id = route.query.id;
        }
        // 如果URL中没有id参数，再检查previewStore中是否有项目ID
        else if (previewStore.getProjectId()) {
            params.id = previewStore.getProjectId();
        }
        
        // 调用保存API
        const result = await addVideoMaterial(params);
        
        // 保存返回的id到previewStore
        if (result.id) {
            previewStore.setProjectId(result.id);
        }

        // 更新预览区文字
        if (result.copywrite) {
            previewStore.setContent(result.copywrite);
        }

        // 更新音乐素材URL
        if (result.musicMaterialUrl) {
            musicStore.updateMusicUrl(result.musicMaterialUrl);
        }

        // 更新配音角色URL
        if (result.audioUrl) {
            previewStore.updateSelectedRoleUrl(result.audioUrl);
        }

        // 更新最后保存的内容
        previewStore.lastSavedTitle = title;
        previewStore.lastSavedContent = content;
        
        // 更新最后保存时间
        previewStore.lastUpdated = new Date().getTime();
        
        // 添加保存成功标志
        previewStore.isSaved = true;
        
        // 记录本次保存状态到localStorage
        localStorage.setItem('content_saved_state', 'true');

        // 显示成功消息
        ElMessage.success('保存成功');
    } catch (error) {
        console.error('保存失败:', error);
        const errorMsg = getErrorMessage(error);
        ElMessage.error(`保存失败: ${errorMsg}`);
    } finally {
        // 重置加载状态
        isLoading.value = false;
    }
};

onMounted(() => {
    // 窗口调整大小时更新状态
    resizeListener = () => {
        windowState.value = {
            width: window.innerWidth,
            pixelRatio: window.devicePixelRatio
        };
    };
    
    window.addEventListener('resize', resizeListener);
    
    // 初始设置
    resizeListener();
});

onBeforeUnmount(() => {
    // 清理事件监听器
    if (resizeListener) {
        window.removeEventListener('resize', resizeListener);
    }
});
</script>

<style lang="scss" scoped>
// 轮播图样式已移至 CarouselBanner.vue 组件

.headbar-container {
    z-index: 200; // 低于轮播图(300)但高于其他元素
    background-color: #ffffff;
    box-shadow: var(--el-box-shadow-light);
    padding: 0 0 0 60px;
    width: 100%;
    // min-height: 60px;
    position: fixed;
    left: 0;
    width: 100%;
    // 使用动态计算的top值
    // top值由headbarTopPosition计算属性提供
    height: 64px;
    &.dark{
        background: #010011;
        box-shadow: 0px 6px 30px 5px rgba(0, 0, 0, 0.05);
    }
    &.digital-human-mode {
        // 数字人模式下的特殊样式
        height: 56px; // 数字人页面专用高度
        padding: 0 20px; // 左右对称的padding
        
        .scrollbar-view {
            position: relative; // 为绝对定位提供基准
        }
        
        .digital-human-nav {
            position: absolute;
            left: 0;
            z-index: 1;
        }
        
        .digital-human-title-edit {
            position: absolute;
            left: 50%;
            transform: translateX(-50%); // 真正的水平居中
            z-index: 1;
        }
        
        .right-container {
            position: absolute;
            right: 0;
            z-index: 1;
        }
    }
    ::v-deep(.scrollbar-view){
        height: 100%;
    }
    // @media (max-width: 1480px), (min-resolution: 1.5dppx) {
    //     min-height: 50px;
    // }
}



// 滚动条视图样式
.scrollbar-view {
    height: 100%;
}

.right-container {
    gap: 20px;
}

.save-button-container {
    display: flex;
    align-items: center;
}

.save-button {
    cursor: pointer;
    color: #353D49;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s;
    
    &:hover {
        opacity: 0.8;
    }
    
    &.disabled {
        cursor: not-allowed;
        opacity: 0.6;
    }
}

.main_top {
    height: 60px;

    //background-color: #fff;
    &_right {
        //width:200px;
        height: 100%;
        padding-right: 5%;

        //background-color: #fff;
        &_item {
            width: 80px;
            height: 30px;
            text-align: center;
            line-height: 30px;
            border-radius: 4px;
            border: 1px solid #00aaaa;
            color: #00aaaa;
        }
    }
}

.main_icons_main {
    height: 70px;
    background-color: rgba(255, 255, 255, 0.4);
    padding: 0 5%;

    &_item {
        flex-shrink: 0;
    }
}

// 数字人导航样式
.digital-human-nav {
    gap: 5px;
    
    .back-btn {
        width: 32px;
        height: 32px;
        justify-content: center;
        border-radius: 4px;
        transition: all 0.3s ease;
        
        .back-icon {
            width: 20px;
            height: 20px;
            color: #666666;
            transition: all 0.3s ease;
        }
        
        &:hover:not(.navigating) {
            // background-color: #f5f5f5;
            
            .back-icon {
                color: #333333;
            }
        }
        
        &.navigating {
            opacity: 0.6;
            cursor: not-allowed;
            
            .back-icon {
                color: #999999;
            }
        }
    }
    
    .digital-human-title {
        font-size: 14px;
        font-weight: 500;
        font-family: 'PingFang SC', sans-serif;
        color: #333333;
        transition: all 0.3s ease;
        
        &:hover:not(.navigating) {
            color: #0AAF60;
        }
        
        &.navigating {
            opacity: 0.6;
            cursor: not-allowed;
            color: #999999;
        }
    }
}

// 数字人标题编辑区域样式
.digital-human-title-edit {
    gap: 8px;
    
    .edit-icon {
        color: #666666;
        flex-shrink: 0;
    }
    
    .title-input {
        border: none;
        outline: none;
        background: transparent;
        font-size: 14px;
        font-weight: 500;
        font-family: 'PingFang SC', sans-serif;
        color: #333333;
        min-width: 120px;
        
        &::placeholder {
            color: #999999;
        }
        
        &:focus {
            border-bottom: 1px solid #0AAF60;
        }
    }
}

// Element Plus 轮播图样式已移至 CarouselBanner.vue 组件
</style>
