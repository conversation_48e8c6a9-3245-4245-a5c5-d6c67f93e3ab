<template>
	<div class="digital-human-transition-app">
		<!-- 顶部导航条区域 -->
		<header class="top-header">
			<Headbar ref="headbarRef" />
		</header>

		<!-- 主要内容区域 -->
		<div class="digital-human-transition-container">
			<div class="transition-content">
				<div class="content-wrapper">
					<h1 class="page-title">数字人功能</h1>
					<p class="page-description">数字人相关功能正在开发中，敬请期待...</p>
					<div class="feature-preview">
						<div class="feature-item">
							<div class="feature-icon">🎭</div>
							<div class="feature-text">
								<h3>数字人创建</h3>
								<p>快速创建专属数字人形象</p>
							</div>
						</div>
						<div class="feature-item">
							<div class="feature-icon">🎬</div>
							<div class="feature-text">
								<h3>视频制作</h3>
								<p>数字人视频内容制作</p>
							</div>
						</div>
						<div class="feature-item">
							<div class="feature-icon">🎤</div>
							<div class="feature-text">
								<h3>语音合成</h3>
								<p>数字人语音配音功能</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { onMounted } from 'vue'
// 导入头部导航组件
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'

// 页面挂载时的初始化逻辑
onMounted(() => {
	console.log('数字人中转页面已加载')
})
</script>

<style scoped lang="scss">
// 全屏应用容器
.digital-human-transition-app {
	width: 100vw;
	height: 100vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	background-color: #ffffff;
}

// 顶部导航区域
.top-header {
	flex-shrink: 0;
	z-index: 1000;
}

.digital-human-transition-container {
	flex: 1;
	width: 100%;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px;
	box-sizing: border-box;
	overflow: auto;
}

.transition-content {
	width: 100%;
	max-width: 1200px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.content-wrapper {
	text-align: center;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20px;
	padding: 60px 40px;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10px);
}

.page-title {
	font-size: 48px;
	font-weight: 700;
	color: #2c3e50;
	margin: 0 0 20px 0;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.page-description {
	font-size: 18px;
	color: #7f8c8d;
	margin: 0 0 50px 0;
	line-height: 1.6;
}

.feature-preview {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
	gap: 30px;
	margin-top: 40px;
}

.feature-item {
	display: flex;
	align-items: center;
	text-align: left;
	padding: 25px;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 15px;
	transition: all 0.3s ease;
	border: 1px solid rgba(255, 255, 255, 0.2);

	&:hover {
		transform: translateY(-5px);
		box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
		background: rgba(255, 255, 255, 0.95);
	}
}

.feature-icon {
	font-size: 40px;
	margin-right: 20px;
	flex-shrink: 0;
}

.feature-text {
	flex: 1;

	h3 {
		font-size: 20px;
		font-weight: 600;
		color: #2c3e50;
		margin: 0 0 8px 0;
	}

	p {
		font-size: 14px;
		color: #7f8c8d;
		margin: 0;
		line-height: 1.5;
	}
}

// 响应式设计
@media (max-width: 768px) {
	.digital-human-transition-container {
		padding: 15px;
	}

	.content-wrapper {
		padding: 40px 25px;
	}

	.page-title {
		font-size: 36px;
	}

	.page-description {
		font-size: 16px;
	}

	.feature-preview {
		grid-template-columns: 1fr;
		gap: 20px;
	}

	.feature-item {
		padding: 20px;
	}
}
</style>
